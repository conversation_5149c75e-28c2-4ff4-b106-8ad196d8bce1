package com.chief.toolkit.fom;

import com.chief.toolkit.fom.exception.JConfigurationException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @program: morpheus
 * @description:
 * @date 2021-10-27 09:41:19
 */
public enum UpdateType {
    UPDATE_STATIC("Static"), UPDATE_NA("NA"), UPDATE_PERIODIC("Periodic"), UPDATE_CONDITIONAL("Conditional");

    @Getter
    private String value;

    private UpdateType(String value) {
        this.value = value;
    }

    public static UpdateType parse(String updateType) {
        switch (updateType.toUpperCase()) {
            case "STATIC":
                return UPDATE_STATIC;
            case "NA":
                return UPDATE_NA;
            case "PERIODIC":
                return UPDATE_PERIODIC;
            case "CONDITIONAL":
                return UPDATE_CONDITIONAL;
            default:
                throw new JConfigurationException(updateType + " is not support in fom[updateType]");
        }
    }
}
