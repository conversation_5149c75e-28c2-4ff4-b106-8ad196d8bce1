package ${pkg()};

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import lombok.Data;
/**
 * ${formatSemantics(datatype().getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
@Data
public class ${datatype().getName()} implements Serializable{

<#list datatype().alternatives as field>
    /**
     * ${formatSemantics(field.getSemantics(), "     * ")}
     */
    private ${getJavaTypeName(field.datatype.name)} ${field.name};

</#list>
    public ${getJavaTypeNameDTO(datatype().getName())} toDTO(){
        return new ${getJavaTypeNameDTO(datatype().getName())}(
        <#list datatype().alternatives as field>
            this.${field.name}<#if !isBasicJavaType(field.datatype.name)>.toDTO()</#if><#if field_has_next>,</#if>
        </#list>
        );
    }
}
