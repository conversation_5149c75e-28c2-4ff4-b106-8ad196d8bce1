package com.chief.toolkit.fom;

import com.chief.toolkit.fom.datatype.IDatatype;

/**
 * <AUTHOR>
 * @program: morpheus
 * @description: 归一化函数定义
 * @date 2022-03-04 15:33:09
 */
public class NormalizationServiceMetadata {
    private String name;

    private IDatatype datatype;

    private int upperBound;

    private String normalization;

    private NormalizationValueEnum value;

    private int handle;

    public NormalizationServiceMetadata(String name, int handle) {
        this.name = name;
        this.handle = handle;
    }

    public String getName() {
        return name;
    }

    public IDatatype getDatatype() {
        return datatype;
    }

    public void setDatatype(IDatatype datatype) {
        this.datatype = datatype;
    }

    public int getUpperBound() {
        return upperBound;
    }

    public void setUpperBound(int upperBound) {
        this.upperBound = upperBound;
    }

    public String getNormalization() {
        return normalization;
    }

    public void setNormalization(String normalization) {
        this.normalization = normalization;
    }

    public NormalizationValueEnum getValue() {
        return value;
    }

    public void setValue(NormalizationValueEnum value) {
        this.value = value;
    }

    public int getHandle() {
        return handle;
    }
}
