package ${pkg()};

<#if hasParent()>
import ${getParentClassFullName()};
</#if>
import java.io.Serializable;
import lombok.Data;
<#if hasParent()>
import lombok.EqualsAndHashCode;
</#if>

/**
 * ${formatSemantics(getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
@Data
public class ${getClassName()}<#if hasParent()> extends ${getParentClassName()}<#else > implements com.chief.model.api.ModelObject</#if> {

<#if hasDeclaredAttributes()>
<#list getDeclaredAttributes() as attribute>
    <#if attribute.getSemantics()?? && attribute.getSemantics()?has_content>
    /**
     * ${formatSemantics(attribute.getSemantics(), "     * ")}
     */
    </#if>
    private ${getJavaTypeName(attribute.datatype.name)} ${attribute.name};

</#list>
</#if>
}
