package com.chief.engine.core.model;


import jakarta.annotation.Nullable;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Actor线程调度层面
 *
 * @param <T>
 */
public abstract class Actor<T> {

    static final int status_idle = 0;
    static final int status_running = 1;

    static final int invocation_async = 0;
    static final int invocation_sync = 1;


    static ForkJoinPool executor = new ForkJoinPool();

    private final Actor<?> executorParent;
    /**
     * 同步子Actor
     */
    private final Set<Actor> syncActors = new HashSet<>();

    private volatile AtomicInteger status = new AtomicInteger(status_idle);

    private ConcurrentLinkedDeque<ActorMessage> messages;


    public Actor() {
        this(null);
    }

    /**
     * @param executorParent 不为空的话，则说明不是独立的Actor，跟父级在同一个调度单元，跟父级之间的调用是线程安全的
     */
    public Actor(@Nullable Actor<?> executorParent) {
        if (executorParent != null) {
            this.executorParent = executorParent;
        } else {
            this.messages = new ConcurrentLinkedDeque<>();
            this.executorParent = null;
        }


    }

    protected abstract boolean onMessage(T message);

    public void send(T message) {
        doSend(new ActorMessage(this, message));
    }

    private void doSend(ActorMessage message) {
        if (executorParent == null) {
            messages.offer(message);
            registerForExecution();
        } else {
            executorParent.doSend(message);
        }
    }

    private void run() {
        try {
            while (true) {
                ActorMessage actorMessage = messages.poll();
                if (actorMessage == null) {
                    break;
                }
                actorMessage.actor().onMessage(actorMessage.msg());
            }
        } finally {
            status.compareAndSet(status_running, status_idle);
            registerForExecution();
        }
    }


    private void registerForExecution() {
        if (status.get() == status_idle && !messages.isEmpty()) {
            if (status.compareAndSet(status_idle, status_running)) {
                executor.execute(this::run);
            }
        }
    }

    private boolean canExecute() {
        return status.get() == status_idle && !messages.isEmpty();
    }


    record ActorMessage(Actor actor, Object msg) {
    }
}
