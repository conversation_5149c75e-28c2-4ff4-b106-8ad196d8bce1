package com.chief.toolkit.fom;

import com.chief.toolkit.fom.exception.JErrorReadingFED;

/**
 * <AUTHOR>
 * @program: morpheus
 * @description:
 * @date 2022-03-04 15:35:22
 */
public enum NormalizationValueEnum {
    Excluded, Included;

    /**
     * If the provided string matches (ignoring case) the name of either
     * order type, that type is returned. Otherwise an exception is thrown
     */
    public static NormalizationValueEnum fromFomString(String fomString) throws JErrorReadingFED {
        if (fomString.isEmpty() || fomString.equalsIgnoreCase("Excluded"))
            return Excluded;
        else if (fomString.equalsIgnoreCase("Included"))
            return Included;
        else
            throw new JErrorReadingFED("Unsupported NormalizationValueEnum found: " + fomString);
    }
}
