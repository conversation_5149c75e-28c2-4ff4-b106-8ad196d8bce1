<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chief</groupId>
        <artifactId>chief-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>chief-maven-plugin</artifactId>
    <packaging>maven-plugin</packaging>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service</artifactId>
            <version>1.1.1</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.google.auto</groupId>
            <artifactId>auto-common</artifactId>
            <version>1.2.1</version>
            <optional>true</optional>
        </dependency>

        <!-- 必须的：Maven Plugin API -->
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-plugin-api</artifactId>
            <version>3.9.6</version>
            <optional>true</optional>
        </dependency>
        <!-- 必须的：用于抽象 Mojo -->
        <dependency>
            <groupId>org.apache.maven.plugin-tools</groupId>
            <artifactId>maven-plugin-annotations</artifactId>
            <version>3.9.0</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-core</artifactId>
            <version>3.9.6</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.chief</groupId>
            <artifactId>chief-toolkit</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-plugin-plugin</artifactId>
                <version>3.9.0</version>
                <executions>
                    <execution>
                        <id>default-descriptor</id>
                        <goals>
                            <goal>descriptor</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                    <release combine.self="override"></release>
                    <compilerArgs>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED</arg>
                    </compilerArgs>

                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.apache.maven.plugin-tools</groupId>
                            <artifactId>maven-plugin-annotations</artifactId>
                            <version>3.9.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>


        </plugins>
    </build>
</project>