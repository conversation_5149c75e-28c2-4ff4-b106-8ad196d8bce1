package com.chief.engine.bootstrap;

import com.chief.engine.core.event.AddModelMsg;
import com.chief.engine.core.model.RootModel;
import com.chief.engine.core.util.ModelJSONDTO;
import com.chief.engine.core.util.ModelJsonUtils;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

import static com.chief.engine.core.constant.ID.ROOT_MODEL_ID;

public class Bootstrap {


    static RootModel rootModel;

    @SneakyThrows
    public static void main(String[] args) {

        rootModel = new RootModel(0, 1000 * 60 * 200L);
        loadScn();
        rootModel.start();

        Thread.sleep(Duration.ofHours(10).toMillis());
    }

    @SneakyThrows
    public static void loadScn() {
        File dir = new File("D:\\project\\csim\\properties");
        File[] files = dir.listFiles();

        List<ModelJSONDTO> result = Collections.synchronizedList(new ArrayList<>());

        CountDownLatch latch = new CountDownLatch(files.length);
        for (File file : files) {
            if (file.getName().endsWith(".json")) {
                Thread.startVirtualThread(() -> {
                    String content = null;
                    try {
                        content = Files.readString(file.toPath());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    ModelJSONDTO model = ModelJsonUtils.parse(content, ModelJSONDTO.class);
                    if (!StringUtils.hasText(model.getObjectInstanceHandle())) {
                        model.setObjectInstanceHandle(UUID.randomUUID().toString());
                    }


                    List<ModelJSONDTO> components = model.getComponents();
                    if (components != null) {
                        components.forEach(component -> {
                            if (!StringUtils.hasText(component.getObjectInstanceHandle())) {
                                component.setObjectInstanceHandle(UUID.randomUUID().toString());
                            }
                        });
                    }

                    result.add(model);
                    latch.countDown();
                });

            }
        }
        latch.await();

        long startTime = System.currentTimeMillis();
        result.forEach(model -> {
            rootModel.send(new AddModelMsg(ROOT_MODEL_ID, model.getObjectInstanceHandle(), model.toModelObject()));
            List<ModelJSONDTO> components = model.getComponents();
            if (components != null) {
                components.forEach(component -> {
                    rootModel.send(new AddModelMsg(model.getObjectInstanceHandle(), component.getObjectInstanceHandle(), component.toModelObject()));
                });
            }
        });
        System.out.println("loadScn time: " + (System.currentTimeMillis() - startTime));

    }
}
