package com.chief.model.api;

import com.chief.toolkit.support.SFunction;

import java.util.List;

/**
 * 对外上下文
 */
public interface ModelContext<T extends ModelObject> {

    /**
     * 获取模型对象
     *
     * @return
     */
    T getModelObject();


    /**
     * 获取父级上下文
     * 一般用于 组件内获取平台
     *
     * @param <P>
     * @return
     */
    <P extends ModelObject> ModelContext<P> getParentModelContext();


    /**
     * 订阅实体
     * @param clazz
     */
    void subscribeObject(Class<? extends ModelObject> clazz);
}
