package com.chief.toolkit.fom.vo;

/**
 * <AUTHOR>
 * @program: morpheus
 * @description:
 * @date 2021-12-21 14:04:19
 */
public class AttributePath {
    private String[] pathArray;

    private int index = 0;

    public AttributePath(String path) {
        pathArray = path.split("\\.");
    }

    public String next() {
        String nextVal = pathArray[index];
        index++;
        return nextVal;
    }

    public String get() {
        return pathArray[index];
    }

    public String getAttributeName() {
        return pathArray[0];
    }

    public boolean hasNext() {
        return index < pathArray.length;
    }

    public void reset() {
        index = 0;
    }

    @Override
    public String toString() {
        return String.join(".", pathArray);
    }
}
