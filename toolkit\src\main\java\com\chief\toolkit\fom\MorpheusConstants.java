package com.chief.toolkit.fom;

/**
 * <AUTHOR>
 * @program: morpheus
 * @description: MorpheusConstants
 * @date 2021-08-11 13:24:59
 */
public class MorpheusConstants {

    /**
     * Consistent value for an invalid handle
     */
    public static final int NULL_HANDLE = -1;

    /**
     * The handle to use for the "RTI"
     */
    public static final int RTI_HANDLE = 0;
    /**
     * Min/Max extent values for use in DDM
     */
    public static final int MIN_EXTENT = 0;
    /**
     * Min/Max extent values for use in DDM
     */
    public static final int MAX_EXTENT = Integer.MAX_VALUE;
    /**
     * 不带region订阅时使用的regionInstanceHandle
     */
    public static final int NO_REGION_INSTANCE_HANDLE = -1;
    /**
     * 不带region订阅时使用的regionServiceName
     */
    public static final String NO_REGION_INSTANCE_SERVICENAME = "none";
    /**
     * Should the ObjectModel types return their qualified names from toString()? If true, they
     * will, otherwise, they'll return the handle values
     */
    public static boolean USE_Q_NAMES = true;
}
